"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, Star, Shield, Target, Award, Users, Clock, MapPin, Phone } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface CourseCardProps {
  name: string;
  duration: string;
  price: string;
  features?: string[];
  category: string;
  type?: string;
  icon: React.ReactNode;
  isPopular?: boolean;
}

const CourseCard = ({
  name,
  duration,
  price,
  features,
  category,
  icon,
  isPopular = false,
}: CourseCardProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ y: -8, scale: 1.02 }}
      className="group relative h-full"
    >
      <Card className={`h-full relative overflow-hidden bg-card border border-border/50 shadow-soft hover:shadow-medium transition-all duration-300 ${
        isPopular ? "ring-2 ring-accent/50 shadow-accent/20" : ""
      }`}>
        {isPopular && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10"
          >
            <Badge className="gradient-accent text-white px-4 py-1 text-xs font-semibold shadow-lg">
              <Star className="w-3 h-3 mr-1 fill-current" />
              Most Popular
            </Badge>
          </motion.div>
        )}

        {/* Category Badge */}
        <div className="absolute top-4 right-4 z-10">
          <Badge variant="outline" className="text-xs bg-background/80 backdrop-blur-sm">
            {category}
          </Badge>
        </div>

        <CardHeader className="text-center pb-4 relative z-10 pt-8">
          <motion.div
            whileHover={{ scale: 1.1, rotate: 5 }}
            className="flex justify-center mb-4"
          >
            <div className="p-4 rounded-2xl bg-primary/10 text-primary group-hover:bg-primary/20 transition-all duration-300 shadow-soft">
              {icon}
            </div>
          </motion.div>
          <CardTitle className="text-xl font-bold text-foreground group-hover:text-primary transition-colors duration-300 mb-2">
            {name}
          </CardTitle>
          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
            <Clock className="w-4 h-4" />
            <span>{duration}</span>
          </div>
        </CardHeader>

        <CardContent className="space-y-6 relative z-10 px-6 pb-6">
          {/* Price Section */}
          <div className="text-center py-6 bg-gradient-to-br from-primary/5 to-accent/5 rounded-xl border border-primary/10">
            <div className="text-4xl font-bold text-primary mb-2">
              {price}
            </div>
            <div className="text-sm text-muted-foreground font-medium uppercase tracking-wider">
              Professional Training
            </div>
          </div>

          {/* Features List */}
          {features && (
            <div className="space-y-3">
              <h4 className="text-sm font-semibold text-foreground uppercase tracking-wider">
                What's Included:
              </h4>
              <ul className="space-y-3">
                {features.map((feature, idx) => (
                  <motion.li
                    key={idx}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: idx * 0.1 }}
                    className="flex items-start text-sm text-foreground/80 group-hover:text-foreground transition-colors duration-300"
                  >
                    <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mr-3 flex-shrink-0 mt-0.5">
                      <Check className="w-3 h-3 text-primary" />
                    </div>
                    <span className="leading-relaxed">{feature}</span>
                  </motion.li>
                ))}
              </ul>
            </div>
          )}

          {/* CTA Button */}
          <div className="pt-4">
            <Button className="w-full btn-modern gradient-primary text-white font-semibold py-3 text-sm hover:shadow-primary/25 transition-all duration-300">
              Enroll Now
            </Button>
            <p className="text-xs text-muted-foreground text-center mt-2">
              PSIRA Certified Training
            </p>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

const ServicesSection = () => {
  const [activeCategory, setActiveCategory] = useState("all");

  const categories = [
    { id: "all", name: "All Courses", icon: <Award className="w-4 h-4" /> },
    { id: "security", name: "Security Training", icon: <Shield className="w-4 h-4" /> },
    { id: "firearm", name: "Firearm Training", icon: <Target className="w-4 h-4" /> },
    { id: "special", name: "Special Courses", icon: <Award className="w-4 h-4" /> },
    { id: "psira", name: "PSIRA Services", icon: <Users className="w-4 h-4" /> },
  ];

  const allCourses = [
    // Security Training Courses
    {
      name: "Grade EDC",
      duration: "3 Weeks",
      price: "R1,950",
      category: "Security Training",
      type: "security",
      icon: <Shield className="w-5 h-5" />,
      features: [
        "Entry Level Training",
        "Basic Security Principles",
        "PSIRA Certification",
      ],
      isPopular: true,
    },
    {
      name: "Grade E-B",
      duration: "4 Weeks",
      price: "R2,900",
      category: "Security Training",
      type: "security",
      icon: <Shield className="w-5 h-5" />,
      features: [
        "Intermediate Training",
        "Advanced Security Protocols",
        "Practical Assessments",
      ],
    },
    {
      name: "Grade E-A",
      duration: "5 Weeks",
      price: "R3,500",
      category: "Security Training",
      type: "security",
      icon: <Shield className="w-5 h-5" />,
      features: [
        "Advanced Training",
        "Leadership Skills",
        "Specialized Techniques",
      ],
    },
    {
      name: "Grade E",
      duration: "5 Days",
      price: "R800",
      category: "Security Training",
      type: "security",
      icon: <Shield className="w-5 h-5" />,
      features: [
        "Basic Security Grade",
        "Quick Certification",
        "Entry Level",
      ],
    },
    {
      name: "Grade D",
      duration: "5 Days",
      price: "R850",
      category: "Security Training",
      type: "security",
      icon: <Shield className="w-5 h-5" />,
      features: [
        "Security Grade D",
        "Professional Training",
        "PSIRA Approved",
      ],
    },
    {
      name: "Grade C",
      duration: "5 Days",
      price: "R900",
      category: "Security Training",
      type: "security",
      icon: <Shield className="w-5 h-5" />,
      features: [
        "Security Grade C",
        "Intermediate Level",
        "Career Advancement",
      ],
    },
    {
      name: "Grade B",
      duration: "5 Days",
      price: "R1,300",
      category: "Security Training",
      type: "security",
      icon: <Shield className="w-5 h-5" />,
      features: [
        "Security Grade B",
        "Advanced Training",
        "Leadership Skills",
      ],
    },
    {
      name: "Grade A",
      duration: "1 Week",
      price: "R1,400",
      category: "Security Training",
      type: "security",
      icon: <Shield className="w-5 h-5" />,
      features: [
        "Highest Security Grade",
        "Management Level",
        "Expert Certification",
      ],
    },
    // Firearm Training Courses
    {
      name: "Hand Gun",
      duration: "3 Days",
      price: "R2,500",
      category: "Firearm Training",
      type: "firearm",
      icon: <Target className="w-5 h-5" />,
      features: ["Safety Protocols", "Marksmanship", "Legal Requirements"],
    },
    {
      name: "Shotgun",
      duration: "3 Days",
      price: "R2,500",
      category: "Firearm Training",
      type: "firearm",
      icon: <Target className="w-5 h-5" />,
      features: ["Weapon Handling", "Maintenance", "Practical Shooting"],
    },
    {
      name: "Rifle",
      duration: "3 Days",
      price: "R2,500",
      category: "Firearm Training",
      type: "firearm",
      icon: <Target className="w-5 h-5" />,
      features: ["Precision Shooting", "Range Safety", "Equipment Care"],
    },
    {
      name: "Hand Gun, Shotgun & Rifle",
      duration: "5 Days",
      price: "R5,500",
      category: "Firearm Training",
      type: "firearm",
      icon: <Target className="w-5 h-5" />,
      features: ["All Three Firearms", "Comprehensive Training", "Best Value"],
      isPopular: true,
    },
    {
      name: "Any 2 Fire Arms",
      duration: "4 Days",
      price: "R4,500",
      category: "Firearm Training",
      type: "firearm",
      icon: <Target className="w-5 h-5" />,
      features: ["Choose Any Two", "Flexible Training", "Cost Effective"],
    },
    {
      name: "Armed Reaction",
      duration: "Specialized",
      price: "R2,000",
      category: "Firearm Training",
      type: "firearm",
      icon: <Target className="w-5 h-5" />,
      features: ["Tactical Training", "Emergency Response", "Professional Skills"],
    },
    // Special Courses
    {
      name: "Instructor",
      duration: "Intensive",
      price: "R5,500",
      category: "Special Courses",
      type: "special",
      icon: <Award className="w-5 h-5" />,
      features: [
        "Teaching Methodology",
        "Curriculum Development",
        "Assessment Skills",
      ],
    },
    {
      name: "Facilitator",
      duration: "Professional",
      price: "R3,500",
      category: "Special Courses",
      type: "special",
      icon: <Award className="w-5 h-5" />,
      features: [
        "Group Management",
        "Communication Skills",
        "Training Delivery",
      ],
    },
    {
      name: "Assessor",
      duration: "Specialized",
      price: "R3,500",
      category: "Special Courses",
      type: "special",
      icon: <Award className="w-5 h-5" />,
      features: [
        "Evaluation Techniques",
        "Standards Compliance",
        "Quality Assurance",
      ],
    },
    {
      name: "Moderator",
      duration: "Advanced",
      price: "R3,500",
      category: "Special Courses",
      type: "special",
      icon: <Award className="w-5 h-5" />,
      features: [
        "Quality Control",
        "Standards Verification",
        "Process Management",
      ],
    },
    // PSIRA Services
    {
      name: "PSIRA Registration",
      duration: "Fast Track",
      price: "R450",
      category: "PSIRA Services",
      type: "psira",
      icon: <Users className="w-5 h-5" />,
      features: ["New Applications", "Document Processing", "Quick Turnaround"],
    },
    {
      name: "PSIRA Renewal",
      duration: "Express",
      price: "R250",
      category: "PSIRA Services",
      type: "psira",
      icon: <Users className="w-5 h-5" />,
      features: ["Renewal Processing", "Status Updates", "Compliance Support"],
    },
  ];

  const filteredCourses = activeCategory === "all"
    ? allCourses
    : allCourses.filter(course => course.type === activeCategory);

  return (
    <section
      id="services"
      className="py-24 px-4 md:px-8 lg:px-16 relative overflow-hidden"
    >
      <div className="container mx-auto max-w-7xl relative z-10">
        {/* Header - Inspired by firearmtrainers.co.za */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="inline-block mb-6">
            <span className="px-6 py-3 bg-primary/10 border border-primary/20 rounded-full text-sm font-semibold text-primary backdrop-blur-sm uppercase tracking-wider">
              Industry Leaders for a Reason
            </span>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="text-foreground">Our</span>
            <br />
            <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              Courses
            </span>
          </h2>
          <div className="max-w-4xl mx-auto">
            <h3 className="text-xl md:text-2xl font-semibold text-foreground mb-4">
              Be Ready. Be Confident. Master Security & Firearm Skills.
            </h3>
            <p className="text-lg text-muted-foreground leading-relaxed">
              IntegraServe 24/7 is proud to offer several instructional courses for individuals of all skill levels.
              Our courses are designed and developed by experts in the field to help you learn the basics and
              advance your professional security career with
              <span className="text-primary font-semibold"> PSIRA certified training</span>.
            </p>
          </div>
        </motion.div>

        {/* Professional Category Filter - Inspired by firearmtrainers.co.za */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-16"
        >
          <div className="bg-card border border-border/50 rounded-2xl p-2 shadow-soft max-w-4xl mx-auto">
            <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
              {categories.map((category) => (
                <motion.button
                  key={category.id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setActiveCategory(category.id)}
                  className={`flex flex-col items-center gap-2 px-4 py-6 rounded-xl font-medium transition-all duration-300 ${
                    activeCategory === category.id
                      ? "gradient-primary text-white shadow-medium"
                      : "bg-transparent text-muted-foreground hover:bg-muted/50 hover:text-foreground"
                  }`}
                >
                  <div className={`p-2 rounded-lg ${
                    activeCategory === category.id
                      ? "bg-white/20"
                      : "bg-primary/10"
                  }`}>
                    {category.icon}
                  </div>
                  <span className="text-sm font-semibold text-center leading-tight">
                    {category.name}
                  </span>
                </motion.button>
              ))}
            </div>
          </div>

          {/* Course Count Indicator */}
          <div className="text-center mt-6">
            <p className="text-sm text-muted-foreground">
              Showing <span className="font-semibold text-primary">{filteredCourses.length}</span> courses
              {activeCategory !== "all" && (
                <span> in <span className="font-semibold">{categories.find(c => c.id === activeCategory)?.name}</span></span>
              )}
            </p>
          </div>
        </motion.div>

        {/* Course Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeCategory}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {filteredCourses.map((course, index) => (
              <CourseCard key={`${activeCategory}-${index}`} {...course} />
            ))}
          </motion.div>
        </AnimatePresence>

        {/* Professional Training Guarantee - Inspired by firearmtrainers.co.za */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mt-20"
        >
          <div className="bg-gradient-to-br from-primary/5 via-accent/5 to-primary/5 rounded-3xl p-12 max-w-6xl mx-auto border border-primary/10">
            <div className="text-center mb-12">
              <div className="inline-flex items-center gap-4 mb-6">
                <div className="p-4 rounded-full gradient-accent shadow-lg">
                  <Award className="w-10 h-10 text-white" />
                </div>
                <div className="text-left">
                  <h3 className="text-3xl md:text-4xl font-bold text-foreground">
                    Professional Training
                  </h3>
                  <p className="text-xl text-primary font-semibold">
                    in South Africa
                  </p>
                </div>
              </div>

              <p className="text-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed mb-8">
                At IntegraServe 24/7, we are dedicated to providing quality security and firearm training that focuses on
                safety, competence, and confidence. Our certified training programs are designed for everyone—from those
                handling firearms for the first time to those with experience. Whether you're looking to meet the legal
                requirements for security work or enhance your skills, our expert instructors provide practical, hands-on
                training to ensure you're fully prepared.
              </p>
            </div>

            {/* Trust Indicators */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {[
                {
                  icon: <Shield className="w-8 h-8" />,
                  title: "PSIRA Certified",
                  description: "All our courses are PSIRA approved and certified"
                },
                {
                  icon: <Users className="w-8 h-8" />,
                  title: "Expert Instructors",
                  description: "Learn from certified professionals with years of experience"
                },
                {
                  icon: <Award className="w-8 h-8" />,
                  title: "Job Placement",
                  description: "Career assistance and job placement support included"
                },
                {
                  icon: <Target className="w-8 h-8" />,
                  title: "Practical Training",
                  description: "Hands-on training with real-world applications"
                },
              ].map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                  className="text-center"
                >
                  <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center mb-4 text-primary">
                    {item.icon}
                  </div>
                  <h4 className="font-semibold text-foreground mb-2">
                    {item.title}
                  </h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {item.description}
                  </p>
                </motion.div>
              ))}
            </div>

            {/* CTA Section */}
            <div className="text-center mt-12">
              <Button className="btn-modern gradient-primary text-white px-8 py-4 text-lg font-semibold shadow-large hover:shadow-primary/25">
                Start Your Training Today
              </Button>
              <p className="text-sm text-muted-foreground mt-4">
                Take your first step today and train with experts you can trust.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ServicesSection;
